import os
import json
import yfinance as yf
import plotly.graph_objects as go
import pandas as pd
from dotenv import load_dotenv
from openai import OpenAI

# ---- Load .env for API key ----
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")

if not api_key:
    raise ValueError("❌ Missing API key! Add OPENAI_API_KEY to .env")

client = OpenAI(api_key=api_key)

# ---- Define functions ----
def get_stock_price(symbol: str):
    """Fetch the latest stock price and % change."""
    ticker = yf.Ticker(symbol)
    info = ticker.info
    return {
        "symbol": symbol.upper(),
        "price": info.get("regularMarketPrice"),
        "currency": info.get("currency"),
        "change": info.get("regularMarketChange"),
        "percent_change": info.get("regularMarketChangePercent"),
        "previous_close": info.get("regularMarketPreviousClose"),
        "market_cap": info.get("marketCap")
    }

def get_company_summary(symbol: str):
    """Fetch company description."""
    ticker = yf.Ticker(symbol)
    info = ticker.info
    return {
        "symbol": symbol.upper(),
        "company_name": info.get("longName"),
        "industry": info.get("industry"),
        "summary": info.get("longBusinessSummary")
    }

def plot_stock_chart(symbol: str, period: str = "1mo", interval: str = "1d"):
    """Plot stock chart using Plotly."""
    ticker = yf.Ticker(symbol)
    hist = ticker.history(period=period, interval=interval)
    hist.reset_index(inplace=True)

    if hist.empty:
        print(f"No data available for {symbol}")
        return {"error": "No data"}

    fig = go.Figure()
    fig.add_trace(go.Candlestick(
        x=hist['Date'],
        open=hist['Open'],
        high=hist['High'],
        low=hist['Low'],
        close=hist['Close'],
        name=symbol
    ))
    fig.update_layout(
        title=f"{symbol.upper()} Stock Price",
        xaxis_title="Date",
        yaxis_title=f"Price ({ticker.info.get('currency', 'USD')})",
        xaxis_rangeslider_visible=False
    )
    fig.show()

    return {"status": "chart displayed"}

# ---- Define OpenAI function schema ----
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_stock_price",
            "description": "Get latest stock price and change info",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock ticker symbol (e.g., AAPL)"}
                },
                "required": ["symbol"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_company_summary",
            "description": "Get company summary information",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock ticker symbol (e.g., TSLA)"}
                },
                "required": ["symbol"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "plot_stock_chart",
            "description": "Plot stock chart using Plotly",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock ticker symbol (e.g., MSFT)"},
                    "period": {
                        "type": "string",
                        "description": "Time period (e.g., 1d, 5d, 1mo, 6mo, 1y, max)",
                        "default": "1mo"
                    },
                    "interval": {
                        "type": "string",
                        "description": "Interval (e.g., 1d, 1h, 30m)",
                        "default": "1d"
                    }
                },
                "required": ["symbol"]
            }
        }
    }
]

# ---- Main chat function ----
def run_chat():
    user_prompt = input("Ask about a stock: ")

    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "You are a helpful stock assistant."},
            {"role": "user", "content": user_prompt}
        ],
        tools=tools,
        tool_choice="auto"
    )

    message = response.choices[0].message

    if message.tool_calls:
        for tool_call in message.tool_calls:
            func_name = tool_call.function.name
            args = json.loads(tool_call.function.arguments)

            if func_name == "get_stock_price":
                data = get_stock_price(args["symbol"])
                print(f"\n📊 {data['symbol']} Latest Price: {data['price']} {data['currency']}")
                print(f"Change: {data['change']} ({data['percent_change']}%)")
                print(f"Previous Close: {data['previous_close']}")
                print(f"Market Cap: {data['market_cap']:,}\n")

            elif func_name == "get_company_summary":
                data = get_company_summary(args["symbol"])
                print(f"\n🏢 {data['company_name']} ({data['symbol']})")
                print(f"Industry: {data['industry']}")
                print(f"About: {data['summary']}\n")

            elif func_name == "plot_stock_chart":
                print(f"\n📈 Plotting chart for {args['symbol']}...")
                plot_stock_chart(args["symbol"], args.get("period", "1mo"), args.get("interval", "1d"))

if __name__ == "__main__":
    run_chat()
